<?php
/**
 * Test script to verify AFYA API login functionality
 * This script tests the API login endpoint and creates admin user if needed
 */

// API endpoint
$api_url = 'https://afyaapi.passdrc.com/api/v1/login';

// Test credentials
$credentials = [
    'email' => '<EMAIL>',
    'password' => 'password'
];

// Alternative credentials to try
$alternative_credentials = [
    ['email' => '<EMAIL>', 'password' => '@!PZaire@!453'],
    ['email' => '<EMAIL>', 'password' => 'password'],
    ['email' => '<EMAIL>', 'password' => 'admin123'],
];

echo "🔍 Testing AFYA API Login...\n";
echo "API URL: $api_url\n\n";

function testLogin($url, $email, $password) {
    $data = json_encode([
        'email' => $email,
        'password' => $password
    ]);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'http_code' => $http_code,
        'error' => $error
    ];
}

// Test primary credentials
echo "1️⃣ Testing primary credentials:\n";
echo "   Email: {$credentials['email']}\n";
echo "   Password: {$credentials['password']}\n";

$result = testLogin($api_url, $credentials['email'], $credentials['password']);

echo "   HTTP Code: {$result['http_code']}\n";
if ($result['error']) {
    echo "   cURL Error: {$result['error']}\n";
}
echo "   Response: " . ($result['response'] ?: 'No response') . "\n\n";

// Test alternative credentials
echo "2️⃣ Testing alternative credentials:\n";
foreach ($alternative_credentials as $index => $cred) {
    echo "   Test " . ($index + 1) . " - Email: {$cred['email']}, Password: {$cred['password']}\n";
    
    $result = testLogin($api_url, $cred['email'], $cred['password']);
    echo "   HTTP Code: {$result['http_code']}\n";
    
    if ($result['response']) {
        $decoded = json_decode($result['response'], true);
        if ($decoded && isset($decoded['status'])) {
            echo "   Status: " . ($decoded['status'] ? 'SUCCESS' : 'FAILED') . "\n";
            if (isset($decoded['message'])) {
                echo "   Message: {$decoded['message']}\n";
            }
        } else {
            echo "   Response: {$result['response']}\n";
        }
    }
    echo "\n";
}

echo "📋 SQL Commands to Create Admin User:\n";
echo "If no credentials work, run these SQL commands in phpMyAdmin:\n\n";

echo "-- Create admin user\n";
echo "INSERT INTO `users` (\n";
echo "    `id`, `f_name`, `l_name`, `email`, `password`, `phone`, `isd_code`, `created_at`, `updated_at`\n";
echo ") VALUES (\n";
echo "    1, 'Admin', 'User', '<EMAIL>', \n";
echo "    '\$2y\$12\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: 'password'\n";
echo "    '1234567890', '+1', NOW(), NOW()\n";
echo ");\n\n";

echo "-- Assign admin role\n";
echo "INSERT INTO `users_role_assign` (\n";
echo "    `id`, `user_id`, `role_id`, `created_at`, `updated_at`\n";
echo ") VALUES (\n";
echo "    1, 1, 14, NOW(), NOW()\n";
echo ");\n\n";

echo "✅ After running the SQL commands, use:\n";
echo "   Email: <EMAIL>\n";
echo "   Password: password\n";
?>
