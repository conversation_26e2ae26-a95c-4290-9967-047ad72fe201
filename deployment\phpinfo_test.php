<?php
// PHP Version Test File
// Upload this to your Hostinger subdomain and access it via browser
// URL: https://afyaapi.passdrc.com/phpinfo_test.php

echo "<h2>PHP Version Information</h2>";
echo "<p><strong>Current PHP Version:</strong> " . phpversion() . "</p>";
echo "<p><strong>Required Version:</strong> >= 8.2.0</p>";

if (version_compare(phpversion(), '8.2.0', '>=')) {
    echo "<p style='color: green;'><strong>✅ PHP Version Compatible!</strong></p>";
    echo "<p>You can now proceed with the AFYA API installation.</p>";
} else {
    echo "<p style='color: red;'><strong>❌ PHP Version Too Old!</strong></p>";
    echo "<p>Please update PHP to version 8.2.0 or higher in your Hostinger control panel.</p>";
}

echo "<hr>";
echo "<h3>Full PHP Info:</h3>";
phpinfo();

// Delete this file after testing for security
?>
