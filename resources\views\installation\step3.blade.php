@extends('layouts.blank')
@section('content')
    <div class="container center-content mainSection">
        <div class="card mt-6">
            <div class="card-body">
                <div class="card-header">
                    <div class="row" style="width: 100%">
                        <div class="col-12">

                            <div class="text-center main-header">
                                <h1>Medicare Software Installation</h1>
                                <h4>Please proceed step by step with proper data according to instructions</h4>
                            </div>
                            <hr>
                        </div>
                    </div>
                </div>
                <div class="row pt-5">
                    <div class="col-3"></div>
                    <div class="col-md-6">
                        @if (session()->has('error'))
                            <div class="row" style="margin-top: 20px;">
                                <div class="col-md-12">
                                    <div class="alert alert-danger">
                                        <strong>Invalid Database Credentials!! </strong>Please check your database
                                        credentials
                                        carefully
                                    </div>
                                </div>
                            </div>
                        @endif



                        <div class="card-main">
                            <div class="d-flex group-main">
                                <div class="group-out">
                                    <div class="where">
                                        <a href="https://help.market.envato.com/hc/en-us/articles/202822600-Where-Is-My-Purchase-Code"
                                            class="text-info">Where to get purchase code ?</a>
                                        </p>
                                    </div>
                                    <p class="step-name">STEP 3. Configure Database</p>
                                    <p class="step-subhead">Provide database information correctly.
                                    </p>
                                    <div class="group">
                                        <div class="grp-form">
                                            <form class="form-section" method="POST" action="{{ route('install.db') }}">
                                                @csrf
                                                <div class="form-group custom-form-group">
                                                    <label class="label-section" for="db_host">Database Host</label>
                                                    <input type="text" class="form-control input-section" id="db_host"
                                                        name="DB_HOST" required autocomplete="off"
                                                        placeholder="Ex: localhost">
                                                    <input type="hidden" name="types[]" value="DB_HOST">
                                                </div>
                                                <div class="form-group custom-form-group">
                                                    <label class="label-section" for="db_name">Database Name</label>
                                                    <input type="text" class="form-control input-section" id="db_name"
                                                        name="DB_DATABASE" required autocomplete="off"
                                                        placeholder="Ex: food_database">
                                                    <input type="hidden" name="types[]" value="DB_DATABASE">
                                                </div>
                                                <div class="form-group custom-form-group">
                                                    <label class="label-section" for="db_user">Database Username</label>
                                                    <input type="text" class="form-control input-section" id="db_user"
                                                        name="DB_USERNAME" required autocomplete="off"
                                                        placeholder="Ex: root">
                                                    <input type="hidden" name="types[]" value="DB_USERNAME">
                                                </div>
                                                <div class="form-group custom-form-group">
                                                    <label class="label-section" for="db_pass">Database Password</label>
                                                    <input type="password" class="form-control input-section" id="db_pass"
                                                        name="DB_PASSWORD" autocomplete="off" placeholder="Ex: password">
                                                    <input type="hidden" name="types[]" value="DB_PASSWORD">
                                                </div>
                                                <div class="text-center">
                                                    <button type="submit" class="btn btn-custom">Continue</button>
                                                </div>
                                            </form>
                                        </div>

                                    </div>
                                </div>
                            </div>

                        </div>


                    </div>

                </div>
            </div>
        </div>
        <link rel="stylesheet" href="{{ asset('css/css.css') }}">

    </div>
@endsection
